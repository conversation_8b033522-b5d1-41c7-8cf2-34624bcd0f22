// Books Store Main JavaScript

// Global variables
let currentPage = 1;
let itemsPerPage = 12;
let currentFilters = {
    category: '',
    priceRange: '',
    search: ''
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize components
    initializeNavigation();
    initializeSearch();
    initializeFilters();
    initializeCart();
    initializeAnimations();
    
    // Load initial data
    loadCategories();
    loadBooks();
    
    console.log('Books Store initialized successfully');
}

// Navigation functions
function initializeNavigation() {
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Active navigation highlighting
    window.addEventListener('scroll', updateActiveNavigation);
    
    // Mobile menu toggle
    const navbarToggler = document.querySelector('.navbar-toggler');
    if (navbarToggler) {
        navbarToggler.addEventListener('click', function() {
            const navbarCollapse = document.querySelector('.navbar-collapse');
            navbarCollapse.classList.toggle('show');
        });
    }
}

function updateActiveNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
}

function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Search functionality
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    }
    
    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }
}

function handleSearch() {
    const searchInput = document.getElementById('searchInput');
    const categoryFilter = document.getElementById('categoryFilter');
    const priceFilter = document.getElementById('priceFilter');

    currentFilters.search = searchInput ? searchInput.value.trim() : '';
    currentFilters.category = categoryFilter ? categoryFilter.value : '';
    currentFilters.priceRange = priceFilter ? priceFilter.value : '';

    currentPage = 1;
    loadBooks();
}

// Filter functionality
function initializeFilters() {
    const categoryFilter = document.getElementById('categoryFilter');
    const priceFilter = document.getElementById('priceFilter');
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', handleSearch);
    }
    
    if (priceFilter) {
        priceFilter.addEventListener('change', handleSearch);
    }
}

// Load categories
function loadCategories() {
    const categoriesGrid = document.getElementById('categoriesGrid');
    if (!categoriesGrid) return;

    let categoriesHTML = '';
    
    Object.keys(booksData).forEach(categoryKey => {
        const category = booksData[categoryKey];
        const bookCount = category.books.length;
        
        categoriesHTML += `
            <div class="category-card" data-category="${categoryKey}">
                <div class="category-icon">
                    <i class="${category.icon}"></i>
                </div>
                <div class="category-content">
                    <h4 class="category-title">${category.name}</h4>
                    <p class="category-description">${category.description}</p>
                    <div class="category-stats">
                        <span class="book-count">${bookCount} كتاب</span>
                    </div>
                </div>
                <div class="category-action">
                    <button class="btn btn-outline-primary" onclick="filterByCategory('${categoryKey}')">
                        <i class="fas fa-eye me-2"></i>
                        عرض الكتب
                    </button>
                </div>
            </div>
        `;
    });
    
    categoriesGrid.innerHTML = categoriesHTML;
}

// Load books
function loadBooks() {
    const booksGrid = document.getElementById('productsGrid');
    if (!booksGrid) return;

    let books = getAllBooks();
    
    // Apply filters
    if (currentFilters.category) {
        books = books.filter(book => book.category === currentFilters.category);
    }
    
    if (currentFilters.search) {
        books = searchBooks(currentFilters.search);
    }
    
    if (currentFilters.priceRange) {
        books = filterBooksByPrice(books, currentFilters.priceRange);
    }
    
    // Pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedBooks = books.slice(startIndex, endIndex);
    
    let booksHTML = '';
    
    paginatedBooks.forEach(book => {
        const stars = generateStars(book.rating);
        const bestseller = book.bestseller ? '<span class="bestseller-badge">الأكثر مبيعاً</span>' : '';
        
        booksHTML += `
            <div class="product-card book-card" data-book-id="${book.id}">
                ${bestseller}
                <div class="book-cover">
                    <div class="book-placeholder">
                        <i class="fas fa-book"></i>
                        <span>${book.name}</span>
                    </div>
                </div>
                <div class="book-content">
                    <div class="book-category">${book.categoryName}</div>
                    <h4 class="book-title">${book.name}</h4>
                    <p class="book-author">بقلم: ${book.author}</p>
                    <p class="book-description">${book.description.substring(0, 100)}...</p>
                    <div class="book-details">
                        <span class="book-pages"><i class="fas fa-file-alt me-1"></i>${book.pages} صفحة</span>
                        <span class="book-age"><i class="fas fa-users me-1"></i>${book.age_group}</span>
                    </div>
                    <div class="book-rating">
                        ${stars}
                        <span class="rating-text">(${book.reviews} تقييم)</span>
                    </div>
                    <div class="book-footer">
                        <div class="book-price">
                            <span class="price">${formatPrice(book.price)}</span>
                        </div>
                        <div class="book-actions">
                            <button class="btn btn-outline-primary btn-sm" onclick="viewBookDetails('${book.id}')">
                                <i class="fas fa-eye me-1"></i>
                                تفاصيل
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="addToCart('${book.id}')">
                                <i class="fas fa-cart-plus me-1"></i>
                                إضافة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    if (booksHTML === '') {
        booksHTML = `
            <div class="col-12 text-center">
                <div class="no-results">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <h4>لا توجد نتائج</h4>
                    <p>لم نجد أي كتب تطابق معايير البحث الخاصة بك</p>
                </div>
            </div>
        `;
    }
    
    booksGrid.innerHTML = booksHTML;
    
    // Update load more button
    updateLoadMoreButton(books.length);
}

// Helper functions
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let starsHTML = '';
    
    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<i class="fas fa-star"></i>';
    }
    
    if (hasHalfStar) {
        starsHTML += '<i class="fas fa-star-half-alt"></i>';
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<i class="far fa-star"></i>';
    }
    
    return starsHTML;
}

function filterByCategory(categoryKey) {
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.value = categoryKey;
    }
    currentFilters.category = categoryKey;
    currentPage = 1;
    loadBooks();
    scrollToSection('products');
}

function updateLoadMoreButton(totalBooks) {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (!loadMoreBtn) return;
    
    const totalPages = Math.ceil(totalBooks / itemsPerPage);
    
    if (currentPage >= totalPages) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
        loadMoreBtn.onclick = function() {
            currentPage++;
            loadBooks();
        };
    }
}

// Animation functions
function initializeAnimations() {
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-up');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.category-card, .book-card, .about-feature').forEach(el => {
        observer.observe(el);
    });
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function formatPrice(price) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0
    }).format(price);
}

// Make functions globally accessible
window.scrollToSection = scrollToSection;
window.filterByCategory = filterByCategory;
