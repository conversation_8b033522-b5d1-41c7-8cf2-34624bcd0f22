// Books Data for the Unique Books Store

const booksData = {
  "animals": {
    "name": "هل تعلم عن الحيوانات",
    "icon": "fas fa-paw",
    "description": "كتب مليئة بالمعلومات المدهشة عن عالم الحيوانات",
    "books": [
      {
        "id": "amazing-animals-facts",
        "name": "حقائق مدهشة عن الحيوانات",
        "pages": 120,
        "price": 35,
        "title": "كتاب شامل عن أغرب الحقائق في عالم الحيوانات",
        "description": "اكتشف أكثر من 200 حقيقة مدهشة عن الحيوانات البرية والبحرية والطيور مع صور ملونة ومعلومات علمية موثقة",
        "author": "د. أحمد الزهراني",
        "categories": ["حيوانات برية", "حيوانات بحرية", "طيور", "حشرات"],
        "age_group": "جميع الأعمار",
        "rating": 4.8,
        "reviews": 156,
        "bestseller": true
      },
      {
        "id": "ocean-creatures",
        "name": "عجائب المحيطات",
        "pages": 95,
        "price": 28,
        "title": "رحلة استكشافية في أعماق المحيطات وكائناتها الغريبة",
        "description": "تعرف على أغرب الكائنات البحرية وأسرار الحياة في أعماق المحيطات مع معلومات علمية مبسطة",
        "author": "د. فاطمة البحري",
        "categories": ["حيوانات بحرية", "محيطات", "أسماك"],
        "age_group": "8 سنوات فما فوق",
        "rating": 4.6,
        "reviews": 89,
        "bestseller": false
      },
      {
        "id": "birds-secrets",
        "name": "أسرار عالم الطيور",
        "pages": 110,
        "price": 32,
        "title": "كل ما تريد معرفته عن الطيور وسلوكياتها المدهشة",
        "description": "استكشف عالم الطيور الساحر وتعلم عن هجراتها وأعشاشها وطرق تواصلها الفريدة",
        "author": "أ. محمد الطائر",
        "categories": ["طيور", "هجرة", "سلوك حيواني"],
        "age_group": "10 سنوات فما فوق",
        "rating": 4.7,
        "reviews": 124,
        "bestseller": false
      }
    ]
  },
  "questions": {
    "name": "أسئلة شائكة وإجاباتها",
    "icon": "fas fa-question-circle",
    "description": "إجابات علمية وموثقة للأسئلة الصعبة والمحيرة",
    "books": [
      {
        "id": "difficult-questions",
        "name": "100 سؤال محير وإجابته",
        "pages": 150,
        "price": 42,
        "title": "إجابات علمية لأصعب الأسئلة التي تخطر على البال",
        "description": "مجموعة من أكثر الأسئلة إثارة للجدل مع إجابات علمية موثقة ومبسطة للجميع",
        "author": "د. سارة العقل",
        "categories": ["علوم", "فلسفة", "تفكير نقدي"],
        "age_group": "12 سنة فما فوق",
        "rating": 4.9,
        "reviews": 203,
        "bestseller": true
      },
      {
        "id": "why-questions",
        "name": "لماذا يحدث هذا؟",
        "pages": 130,
        "price": 38,
        "title": "تفسيرات علمية للظواهر الغريبة من حولنا",
        "description": "اكتشف الأسباب العلمية وراء الظواهر الطبيعية والاجتماعية التي نراها يومياً",
        "author": "د. خالد المفكر",
        "categories": ["ظواهر طبيعية", "علوم", "فضول"],
        "age_group": "10 سنوات فما فوق",
        "rating": 4.5,
        "reviews": 78,
        "bestseller": false
      }
    ]
  },
  "general": {
    "name": "معلومات عامة",
    "icon": "fas fa-lightbulb",
    "description": "كتب تحتوي على معلومات مفيدة ومتنوعة",
    "books": [
      {
        "id": "amazing-facts",
        "name": "1000 معلومة مدهشة",
        "pages": 200,
        "price": 45,
        "title": "موسوعة شاملة للمعلومات المدهشة في جميع المجالات",
        "description": "مجموعة ضخمة من المعلومات المدهشة في العلوم والتاريخ والجغرافيا والثقافة العامة",
        "author": "فريق الكتاب المعرفي",
        "categories": ["معلومات عامة", "ثقافة", "علوم"],
        "age_group": "جميع الأعمار",
        "rating": 4.8,
        "reviews": 267,
        "bestseller": true
      },
      {
        "id": "world-records",
        "name": "أرقام قياسية مذهلة",
        "pages": 85,
        "price": 30,
        "title": "أغرب وأعجب الأرقام القياسية في العالم",
        "description": "تعرف على أغرب الأرقام القياسية التي حققها البشر والطبيعة عبر التاريخ",
        "author": "أ. نادر الرقمي",
        "categories": ["أرقام قياسية", "إنجازات", "تاريخ"],
        "age_group": "8 سنوات فما فوق",
        "rating": 4.4,
        "reviews": 92,
        "bestseller": false
      }
    ]
  },
  "culture": {
    "name": "ثقافة وتاريخ",
    "icon": "fas fa-landmark",
    "description": "كتب تاريخية وثقافية مبسطة ومفيدة",
    "books": [
      {
        "id": "ancient-civilizations",
        "name": "حضارات قديمة مذهلة",
        "pages": 160,
        "price": 40,
        "title": "رحلة عبر أعظم الحضارات في التاريخ الإنساني",
        "description": "استكشف أسرار الحضارات القديمة من الفراعنة إلى البابليين والإغريق والرومان",
        "author": "د. ليلى التاريخ",
        "categories": ["تاريخ", "حضارات", "آثار"],
        "age_group": "12 سنة فما فوق",
        "rating": 4.7,
        "reviews": 145,
        "bestseller": false
      },
      {
        "id": "arab-heritage",
        "name": "كنوز التراث العربي",
        "pages": 140,
        "price": 36,
        "title": "أجمل ما في التراث العربي من قصص وحكم وأمثال",
        "description": "مجموعة مختارة من أجمل القصص والحكم والأمثال من التراث العربي الأصيل",
        "author": "أ. عبدالله التراثي",
        "categories": ["تراث عربي", "حكم", "أمثال"],
        "age_group": "جميع الأعمار",
        "rating": 4.6,
        "reviews": 118,
        "bestseller": false
      }
    ]
  },
  "science": {
    "name": "علوم مبسطة",
    "icon": "fas fa-atom",
    "description": "كتب علمية مبسطة للجميع",
    "books": [
      {
        "id": "space-mysteries",
        "name": "أسرار الفضاء",
        "pages": 125,
        "price": 38,
        "title": "رحلة مثيرة لاستكشاف أسرار الكون والفضاء",
        "description": "تعرف على أسرار النجوم والكواكب والمجرات بطريقة مبسطة ومشوقة",
        "author": "د. نجم الفضاء",
        "categories": ["فضاء", "فلك", "كواكب"],
        "age_group": "10 سنوات فما فوق",
        "rating": 4.8,
        "reviews": 189,
        "bestseller": true
      },
      {
        "id": "human-body",
        "name": "عجائب جسم الإنسان",
        "pages": 115,
        "price": 34,
        "title": "اكتشف المعجزات المذهلة في جسم الإنسان",
        "description": "تعلم عن أجهزة الجسم ووظائفها المدهشة بطريقة علمية مبسطة",
        "author": "د. أمل الطبيب",
        "categories": ["جسم الإنسان", "طب", "صحة"],
        "age_group": "8 سنوات فما فوق",
        "rating": 4.5,
        "reviews": 134,
        "bestseller": false
      }
    ]
  },
  "development": {
    "name": "تطوير الذات",
    "icon": "fas fa-user-graduate",
    "description": "كتب لتطوير الشخصية والمهارات",
    "books": [
      {
        "id": "thinking-skills",
        "name": "مهارات التفكير الإبداعي",
        "pages": 180,
        "price": 48,
        "title": "طور قدراتك على التفكير الإبداعي والنقدي",
        "description": "تعلم أساليب التفكير الإبداعي وحل المشكلات بطرق مبتكرة ومؤثرة",
        "author": "د. منى المبدعة",
        "categories": ["تفكير إبداعي", "حل مشكلات", "إبداع"],
        "age_group": "14 سنة فما فوق",
        "rating": 4.7,
        "reviews": 156,
        "bestseller": false
      }
    ]
  },
  "kids": {
    "name": "كتب الأطفال",
    "icon": "fas fa-child",
    "description": "كتب تعليمية وترفيهية للأطفال",
    "books": [
      {
        "id": "kids-animals",
        "name": "حيواناتي المفضلة",
        "pages": 60,
        "price": 25,
        "title": "كتاب ملون عن الحيوانات للأطفال",
        "description": "كتاب مصور وملون يعرف الأطفال على الحيوانات المختلفة بطريقة مرحة وتعليمية",
        "author": "أ. نورا الطفولة",
        "categories": ["أطفال", "حيوانات", "تعليم"],
        "age_group": "3-8 سنوات",
        "rating": 4.9,
        "reviews": 234,
        "bestseller": true
      }
    ]
  }
};

// Function to get all books from all categories
function getAllBooks() {
  const allBooks = [];
  Object.keys(booksData).forEach(categoryKey => {
    const category = booksData[categoryKey];
    category.books.forEach(book => {
      allBooks.push({
        ...book,
        category: categoryKey,
        categoryName: category.name
      });
    });
  });
  return allBooks;
}

// Function to get books by category
function getBooksByCategory(categoryKey) {
  return booksData[categoryKey] ? booksData[categoryKey].books : [];
}

// Function to get book by ID
function getBookById(bookId) {
  const allBooks = getAllBooks();
  return allBooks.find(book => book.id === bookId);
}

// Function to search books
function searchBooks(query) {
  const allBooks = getAllBooks();
  const searchTerm = query.toLowerCase();
  
  return allBooks.filter(book => 
    book.name.toLowerCase().includes(searchTerm) ||
    book.title.toLowerCase().includes(searchTerm) ||
    book.description.toLowerCase().includes(searchTerm) ||
    book.author.toLowerCase().includes(searchTerm) ||
    book.categories.some(cat => cat.toLowerCase().includes(searchTerm))
  );
}

// Function to filter books by price range
function filterBooksByPrice(books, priceRange) {
  if (!priceRange) return books;
  
  const [min, max] = priceRange.split('-').map(p => p.replace('+', ''));
  
  return books.filter(book => {
    if (max) {
      return book.price >= parseInt(min) && book.price <= parseInt(max);
    } else {
      return book.price >= parseInt(min);
    }
  });
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    booksData,
    getAllBooks,
    getBooksByCategory,
    getBookById,
    searchBooks,
    filterBooksByPrice
  };
}
