/* Database Store Main Styles */

:root {
    --primary-color: #8b4513;
    --secondary-color: #a0522d;
    --accent-color: #daa520;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --light-color: #faf7f2;
    --dark-color: #3e2723;
    --gray-100: #f5f1eb;
    --gray-200: #ede7db;
    --gray-300: #d7c7b8;
    --gray-400: #a1887f;
    --gray-500: #8d6e63;
    --gray-600: #6d4c41;
    --gray-700: #5d4037;
    --gray-800: #4e342e;
    --gray-900: #3e2723;
    
    --font-primary: 'Cairo', sans-serif;
    --font-secondary: 'Tajawal', sans-serif;
    
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--light-color);
    direction: rtl;
    text-align: right;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

/* Buttons */
.btn {
    font-family: var(--font-secondary);
    font-weight: 600;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.btn-outline-light {
    border: 2px solid white;
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Header */
.header-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 1rem 0;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-family: var(--font-secondary);
    font-size: 1.5rem;
    font-weight: 700;
    color: white !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: white !important;
}

.dropdown-menu {
    background: white;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    margin-top: 0.5rem;
}

.dropdown-item {
    color: var(--gray-700);
    padding: 0.75rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: var(--primary-color);
    color: white;
}

.dropdown-divider {
    margin: 0.5rem 0;
    border-color: var(--gray-200);
}

.cart-icon {
    position: relative;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.cart-icon:hover {
    background: rgba(255, 255, 255, 0.1);
}

.cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--accent-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.05)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-title .highlight {
    color: var(--accent-color);
    display: block;
    font-size: 2.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.1rem;
}

.feature-item i {
    color: var(--accent-color);
    font-size: 1.25rem;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stats-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Search Section */
.search-section {
    padding: 3rem 0;
    background: white;
    margin-top: -2rem;
    position: relative;
    z-index: 10;
}

.search-card {
    background: white;
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-200);
}

.search-input {
    position: relative;
}

.search-input i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    z-index: 2;
}

.search-input input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-select {
    padding: 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Sections */
.section-header {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Categories Section */
.categories-section {
    padding: 5rem 0;
    background: var(--gray-100);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

/* Products Section */
.products-section {
    padding: 5rem 0;
    background: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* About Section */
.about-section {
    padding: 5rem 0;
    background: var(--gray-100);
}

.about-title {
    font-size: 2.5rem;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
}

.about-text {
    font-size: 1.1rem;
    color: var(--gray-600);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.about-feature {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.feature-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.feature-content h4 {
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.feature-content p {
    color: var(--gray-600);
    margin: 0;
}

.about-image {
    display: flex;
    align-items: center;
    justify-content: center;
}

.trust-badges {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
}

.trust-badge {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.trust-badge i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.trust-badge span {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
}

/* Contact Section */
.contact-section {
    padding: 5rem 0;
    background: white;
}

.contact-card {
    background: white;
    border-radius: var(--border-radius-xl);
    padding: 3rem;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-200);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--gray-700);
}

.form-control {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: var(--font-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Footer */
.footer-section {
    background: var(--gray-900);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-brand h3 {
    color: white;
    margin-bottom: 1rem;
}

.footer-brand p {
    color: var(--gray-400);
    margin: 0;
}

.footer-links h4,
.footer-contact h4,
.footer-social h4 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--gray-400);
}

.contact-item i {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid var(--gray-700);
    margin-top: 2rem;
    padding-top: 1rem;
}

.footer-bottom p {
    color: var(--gray-400);
    margin: 0;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-title .highlight {
        font-size: 1.75rem;
    }
    
    .hero-actions {
        flex-direction: column;
    }
    
    .hero-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .stats-card {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .about-feature {
        flex-direction: column;
        text-align: center;
    }
    
    .search-card .row > div {
        margin-bottom: 1rem;
    }
    
    .categories-grid,
    .products-grid {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
